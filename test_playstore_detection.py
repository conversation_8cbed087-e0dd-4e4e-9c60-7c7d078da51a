#!/usr/bin/env python3
"""
测试Google Play Store检测功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pages.base.app_detector import AppDetector
from core.logger import log


def test_playstore_detection():
    """测试Google Play Store检测功能"""
    print("=" * 60)
    print("测试Google Play Store应用检测功能")
    print("=" * 60)
    
    detector = AppDetector()
    
    try:
        # 测试Google Play Store检测
        print("\n🔍 开始检测Google Play Store应用...")
        result = detector.check_google_playstore_app_opened()
        
        if result:
            print("✅ 检测结果: Google Play Store应用正在运行")
        else:
            print("❌ 检测结果: Google Play Store应用未运行")
            
        print(f"\n📊 检测结果: {result}")
        
        # 额外测试：查找可用的应用
        print("\n🔍 查找设备上可用的应用...")
        try:
            # 这里可以添加更多的测试逻辑
            print("测试完成")
        except Exception as e:
            print(f"查找应用时出错: {e}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        log.error(f"测试失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    return True


if __name__ == "__main__":
    test_playstore_detection()
